#!/usr/bin/env python3
"""
Standalone Gemini Native Audio Console Application

This application allows direct voice conversation with Gemini 2.5 Flash Preview Native Audio Dialog
through the console using real-time audio streaming.

Requirements:
- pip install google-genai pyaudio numpy

Usage:
- Set GEMINI_API_KEY environment variable
- Run: python standalone.py
- Speak into your microphone
- Type 'q' and press En<PERSON> to quit
"""

import os
import asyncio
import logging
import signal
import sys
from typing import Optional

import pyaudio
import numpy as np
from google import genai
from google.genai import types

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Audio configuration
FORMAT = pyaudio.paInt16
CHANNELS = 1
SEND_SAMPLE_RATE = 16000  # Input audio rate
RECEIVE_SAMPLE_RATE = 24000  # Output audio rate from Gemini
CHUNK_SIZE = 1024

# Gemini configuration
MODEL = "gemini-2.5-flash-preview-native-audio-dialog"
VOICE = "Zephyr"  # Available voices: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>hyr


class Gemini<PERSON>udioChat:
    """Real-time audio chat with Gemini using native audio dialog"""

    def __init__(self, api_key: str, voice: str = VOICE):
        self.api_key = api_key
        self.voice = voice
        self.client = None
        self.session = None
        self.pya = pyaudio.PyAudio()

        # Audio streams
        self.input_stream = None
        self.output_stream = None

        # Queues for audio processing
        self.audio_in_queue = None
        self.audio_out_queue = None

        # Control flags
        self.running = False
        self.speaking = False

    async def initialize(self):
        """Initialize Gemini client and session"""
        try:
            logger.info("🤖 Initializing Gemini Native Audio Chat...")

            # Initialize Gemini client
            self.client = genai.Client(
                api_key=self.api_key,
                http_options={"api_version": "v1beta"}
            )

            # Configure for native audio dialog
            config = types.LiveConnectConfig(
                response_modalities=["AUDIO"],
                speech_config=types.SpeechConfig(
                    voice_config=types.VoiceConfig(
                        prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name=self.voice)
                    )
                ),
                realtime_input_config=types.RealtimeInputConfig(
                    turn_coverage="TURN_INCLUDES_ALL_INPUT"
                ),
            )

            # Start connection
            self._connection_manager = self.client.aio.live.connect(
                model=MODEL,
                config=config
            )

            # Start the session
            self.session = await self._connection_manager.__aenter__()

            logger.info("✅ Gemini Native Audio Chat initialized successfully")
            logger.info(f"🎤 Using voice: {self.voice}")
            logger.info(f"🤖 Using model: {MODEL}")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini: {e}")
            return False

    async def setup_audio(self):
        """Setup audio input and output streams"""
        try:
            logger.info("🎵 Setting up audio streams...")

            # Setup input stream (microphone)
            mic_info = self.pya.get_default_input_device_info()
            self.input_stream = await asyncio.to_thread(
                self.pya.open,
                format=FORMAT,
                channels=CHANNELS,
                rate=SEND_SAMPLE_RATE,
                input=True,
                input_device_index=mic_info["index"],
                frames_per_buffer=CHUNK_SIZE,
            )

            # Setup output stream (speakers)
            self.output_stream = await asyncio.to_thread(
                self.pya.open,
                format=FORMAT,
                channels=CHANNELS,
                rate=RECEIVE_SAMPLE_RATE,
                output=True,
                frames_per_buffer=CHUNK_SIZE,
            )

            # Initialize audio queues
            self.audio_in_queue = asyncio.Queue()
            self.audio_out_queue = asyncio.Queue(maxsize=10)

            logger.info("✅ Audio streams setup successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to setup audio: {e}")
            return False

    async def listen_microphone(self):
        """Continuously capture audio from microphone and send to Gemini"""
        try:
            logger.info("🎤 Starting microphone capture...")

            while self.running:
                if not self.speaking:  # Don't capture while Gemini is speaking
                    try:
                        # Read audio data from microphone
                        data = await asyncio.to_thread(
                            self.input_stream.read,
                            CHUNK_SIZE,
                            exception_on_overflow=False
                        )

                        # Send to Gemini
                        await self.audio_out_queue.put({
                            "data": data,
                            "mime_type": f"audio/pcm;rate={SEND_SAMPLE_RATE}"
                        })

                    except Exception as e:
                        if self.running:  # Only log if we're still supposed to be running
                            logger.debug(f"Microphone read error: {e}")

                await asyncio.sleep(0.01)  # Small delay to prevent busy waiting

        except Exception as e:
            logger.error(f"Error in microphone capture: {e}")

    async def send_audio_to_gemini(self):
        """Send captured audio to Gemini"""
        try:
            logger.info("📤 Starting audio transmission to Gemini...")

            while self.running:
                try:
                    # Get audio data from queue
                    audio_msg = await asyncio.wait_for(
                        self.audio_out_queue.get(),
                        timeout=0.1
                    )

                    # Send to Gemini
                    await self.session.send_realtime_input(
                        audio=types.Blob(
                            data=audio_msg["data"],
                            mime_type=audio_msg["mime_type"]
                        )
                    )

                except asyncio.TimeoutError:
                    continue  # No audio data available, continue
                except Exception as e:
                    if self.running:
                        logger.error(f"Error sending audio to Gemini: {e}")

        except Exception as e:
            logger.error(f"Error in audio transmission: {e}")

    async def receive_audio_from_gemini(self):
        """Receive audio responses from Gemini"""
        try:
            logger.info("📥 Starting audio reception from Gemini...")

            while self.running:
                try:
                    turn = self.session.receive()
                    async for response in turn:
                        if response.data is not None:
                            # Queue audio for playback
                            await self.audio_in_queue.put(response.data)

                        if response.text is not None:
                            # Print any text responses
                            print(f"🤖 Gemini: {response.text}")

                except Exception as e:
                    if self.running:
                        logger.error(f"Error receiving from Gemini: {e}")
                        await asyncio.sleep(1)  # Wait before retrying

        except Exception as e:
            logger.error(f"Error in audio reception: {e}")

    async def play_audio(self):
        """Play audio responses from Gemini"""
        try:
            logger.info("🔊 Starting audio playback...")

            while self.running:
                try:
                    # Get audio data from queue
                    audio_data = await asyncio.wait_for(
                        self.audio_in_queue.get(),
                        timeout=0.1
                    )

                    self.speaking = True
                    logger.debug(f"🔊 Playing {len(audio_data)} bytes of audio")

                    # Play audio
                    await asyncio.to_thread(self.output_stream.write, audio_data)

                    self.speaking = False

                except asyncio.TimeoutError:
                    continue  # No audio data available, continue
                except Exception as e:
                    self.speaking = False
                    if self.running:
                        logger.error(f"Error playing audio: {e}")

        except Exception as e:
            logger.error(f"Error in audio playback: {e}")

    async def handle_user_input(self):
        """Handle text input from user"""
        try:
            while self.running:
                try:
                    # Get user input
                    user_input = await asyncio.to_thread(
                        input,
                        "💬 Type message (or 'q' to quit): "
                    )

                    if user_input.lower() in ['q', 'quit', 'exit']:
                        logger.info("👋 User requested exit")
                        self.running = False
                        break

                    if user_input.strip():
                        # Send text message to Gemini
                        await self.session.send_client_content(
                            turns={"role": "user", "parts": [{"text": user_input}]},
                            turn_complete=True
                        )

                except EOFError:
                    # Handle Ctrl+D
                    logger.info("👋 EOF received, exiting")
                    self.running = False
                    break
                except Exception as e:
                    logger.error(f"Error handling user input: {e}")

        except Exception as e:
            logger.error(f"Error in user input handler: {e}")

    async def run(self):
        """Main run loop"""
        try:
            # Initialize everything
            if not await self.initialize():
                return False

            if not await self.setup_audio():
                return False

            self.running = True

            # Send initial greeting
            await self.session.send_client_content(
                turns={"role": "user", "parts": [{"text": "Hello! I'm ready to chat with you using voice."}]},
                turn_complete=True
            )

            logger.info("🎉 Gemini Audio Chat is ready!")
            logger.info("🎤 Speak into your microphone to chat")
            logger.info("💬 Type messages and press Enter")
            logger.info("❌ Type 'q' and press Enter to quit")

            # Start all tasks
            async with asyncio.TaskGroup() as tg:
                tg.create_task(self.listen_microphone())
                tg.create_task(self.send_audio_to_gemini())
                tg.create_task(self.receive_audio_from_gemini())
                tg.create_task(self.play_audio())
                tg.create_task(self.handle_user_input())

        except* Exception as eg:
            logger.error(f"Error in main run loop: {eg}")
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("🧹 Cleaning up...")

            self.running = False

            # Close audio streams
            if self.input_stream:
                await asyncio.to_thread(self.input_stream.close)
            if self.output_stream:
                await asyncio.to_thread(self.output_stream.close)

            # Close PyAudio
            if self.pya:
                await asyncio.to_thread(self.pya.terminate)

            # Close Gemini session
            if self._connection_manager and self.session:
                await self._connection_manager.__aexit__(None, None, None)

            logger.info("✅ Cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


async def main():
    """Main function"""
    # Check for API key (try both GEMINI_API_KEY and GOOGLE_API_KEY)
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        logger.error("❌ GEMINI_API_KEY or GOOGLE_API_KEY environment variable not set")
        logger.info("💡 Set it with: export GEMINI_API_KEY='your-api-key' or export GOOGLE_API_KEY='your-api-key'")
        return

    # Create and run chat
    chat = GeminiAudioChat(api_key)

    # Handle graceful shutdown
    def signal_handler(signum, frame):
        logger.info("🛑 Received interrupt signal, shutting down...")
        chat.running = False

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        await chat.run()
    except KeyboardInterrupt:
        logger.info("👋 Interrupted by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
