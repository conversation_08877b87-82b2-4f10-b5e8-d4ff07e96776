#!/usr/bin/env python3
"""
Setup script for Voice Receptionist Agent
"""
import os
import sys
import subprocess
import shutil

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def check_dependencies():
    """Check if required system dependencies are installed"""
    print("🔍 Checking system dependencies...")

    # Check if brew is available (macOS)
    if sys.platform == "darwin":
        if shutil.which("brew") is None:
            print("❌ Homebrew not found. Please install Homebrew first:")
            print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False

        # Check if portaudio is installed
        result = subprocess.run("brew list portaudio", shell=True, capture_output=True)
        if result.returncode != 0:
            print("📦 Installing PortAudio...")
            if not run_command("brew install portaudio", "PortAudio installation"):
                return False

    print("✅ System dependencies check completed")
    return True

def install_python_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")

    # Install requirements
    if not run_command("pip install -r requirements.txt", "Python dependencies installation"):
        return False

    return True

def setup_environment():
    """Set up environment configuration"""
    print("⚙️ Setting up environment configuration...")

    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from .env.example")
            print("📝 Please edit .env file and add your credentials:")
            print("   - LIVEKIT_URL")
            print("   - LIVEKIT_API_KEY")
            print("   - LIVEKIT_API_SECRET")
            print("   - GOOGLE_API_KEY")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file already exists")

    return True

def run_tests():
    """Run configuration tests"""
    print("🧪 Running configuration tests...")

    result = subprocess.run("python test_config.py", shell=True, capture_output=True, text=True)
    print(result.stdout)

    if result.returncode == 0:
        print("✅ All tests passed! Your setup is ready.")
        return True
    else:
        print("⚠️ Some tests failed. This is normal if you haven't configured your credentials yet.")
        print("✅ Basic setup is complete - you can configure credentials later.")
        return True  # Don't fail setup for missing credentials

def main():
    """Main setup function"""
    print("🎤 Voice Receptionist Agent Setup")
    print("=" * 50)

    steps = [
        ("System Dependencies", check_dependencies),
        ("Python Dependencies", install_python_dependencies),
        ("Environment Configuration", setup_environment),
        ("Configuration Tests", run_tests),
    ]

    for step_name, step_func in steps:
        print(f"\n📋 Step: {step_name}")
        if not step_func():
            print(f"❌ Setup failed at step: {step_name}")
            sys.exit(1)

    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your credentials")
    print("2. Run: python test_config.py (to verify configuration)")
    print("3. Run: python main.py dev (to start the agent)")
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()
