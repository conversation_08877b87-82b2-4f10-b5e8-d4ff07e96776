# 🚀 Quick Start Guide

Get your Voice Receptionist Agent running in 5 minutes!

## 1. Setup (One-time)

Run the automated setup:
```bash
python setup.py
```

This will:
- ✅ Install all dependencies
- ✅ Create your `.env` configuration file
- ✅ Verify everything is working

## 2. Get Your Credentials

### LiveKit Cloud (Free tier available)
1. Go to [LiveKit Cloud](https://cloud.livekit.io)
2. Sign up and create a new project
3. Copy your:
   - **Project URL** (e.g., `wss://your-project.livekit.cloud`)
   - **API Key**
   - **API Secret**

### Google Gemini API (Free tier available)
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Create an API key
3. Copy your **API Key**

## 3. Configure

Edit your `.env` file:
```bash
# Replace these with your actual credentials
LIVEKIT_URL=wss://your-project.livekit.cloud
LIVEKIT_API_KEY=your_actual_api_key
LIVEKIT_API_SECRET=your_actual_api_secret
GOOGLE_API_KEY=your_actual_google_api_key
```

## 4. Test Configuration

Verify everything is set up correctly:
```bash
python test_config.py
```

You should see:
```
✓ livekit.agents imported successfully
✓ livekit.plugins.google imported successfully
✓ python-dotenv imported successfully
✓ Config imported successfully
✓ .env file found
✓ Configuration validation passed
✓ Gemini model initialized successfully
```

## 5. Start the Agent

Run your voice receptionist:
```bash
python main.py dev
```

You should see:
```
Starting Voice Receptionist Agent...
Agent is ready and waiting for connections
```

## 6. Test with a Client

### Option A: Use the HTML Test Client
1. Open `client_example.html` in your browser
2. Fill in your LiveKit URL and a test token
3. Click "Connect to Receptionist"
4. Start talking!

### Option B: Generate a Test Token
Use the LiveKit CLI to generate a test token:
```bash
# Install LiveKit CLI
npm install -g @livekit/cli

# Generate a test token (replace with your credentials)
livekit-cli token create \
  --api-key your_api_key \
  --api-secret your_api_secret \
  --identity test-user \
  --room receptionist-room
```

## 🎯 What to Expect

Once connected, your voice receptionist will:
1. **Greet you**: "Hello! Welcome to our service..."
2. **Listen actively**: Uses native audio processing
3. **Respond naturally**: Powered by Gemini 2.5 Flash
4. **Handle interruptions**: You can interrupt and it will respond appropriately

## 🗣️ Try These Phrases

- "Hello, how are you today?"
- "Can you help me with information about your services?"
- "What can you assist me with?"
- "I need help with..."
- "Thank you, have a great day!"

## 🔧 Troubleshooting

### "Missing required environment variables"
- Check that your `.env` file has real values (not placeholders)
- Make sure there are no extra spaces around the `=` signs

### "Connection failed"
- Verify your LiveKit URL is correct
- Check that your API credentials are valid
- Ensure your firewall allows WebSocket connections

### "Audio not working"
- Grant microphone permissions in your browser
- Check that your audio devices are working
- Try refreshing the page

### "Agent not responding"
- Check the agent logs for errors
- Verify your Google API key is valid
- Make sure the agent is running (`python main.py dev`)

## 📚 Next Steps

- **Customize the voice**: Edit `GEMINI_VOICE` in `config.py`
- **Modify personality**: Update `SYSTEM_INSTRUCTIONS` in `config.py`
- **Add features**: Extend the agent with custom tools and functions
- **Deploy**: Set up production deployment with proper scaling

## 🆘 Need Help?

1. Check the full `README.md` for detailed documentation
2. Run `python test_config.py` to diagnose issues
3. Check the agent logs for error messages
4. Verify all credentials are correct and active

---

**🎉 That's it! You now have a working voice receptionist powered by Gemini 2.5 Flash and LiveKit!**
