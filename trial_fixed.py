#!/usr/bin/env python3
"""
Fixed LiveKit Voice Receptionist Agent using Official Gemini Plugin

This implementation uses LiveKit's official Google plugin for Gemini Live API
instead of a custom implementation, ensuring proper audio streaming.
"""

import asyncio
import logging
import os
from typing import Annotated

from livekit import rtc
from livekit.agents import (
    AutoSubscribe,
    JobContext,
    WorkerOptions,
    cli,
    llm,
)
from livekit.agents.voice_assistant import VoiceAssistant
from livekit.plugins import google

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Config:
    """Configuration for the voice receptionist agent"""
    
    # Gemini model configuration
    MODEL = "gemini-2.0-flash-exp"  # Latest Gemini Live API model
    VOICE = "Zephyr"  # Available voices: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Zephyr
    TEMPERATURE = 0.7
    
    # System instructions for the voice receptionist
    SYSTEM_INSTRUCTIONS = """You are a professional and friendly voice receptionist for our company. 

Your role is to:
- Greet visitors warmly and professionally
- Help them with their inquiries
- Direct them to the appropriate department or person
- Provide general information about our services
- Handle appointment scheduling requests
- Maintain a helpful and courteous tone at all times

Keep your responses concise but informative. Always be polite and professional.
If you don't know something, politely say so and offer to connect them with someone who can help.

Start by greeting the user warmly when they first connect."""


async def entrypoint(ctx: JobContext):
    """Main entrypoint for the voice receptionist agent"""
    logger.info(f"🚀 Starting Voice Receptionist Agent in room: {ctx.room.name}")
    
    # Verify API key is available
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        logger.error("❌ GOOGLE_API_KEY environment variable not set")
        return
    
    # Connect to the room first
    await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
    logger.info("✅ Connected to LiveKit room")
    
    # Create the Gemini Live API model
    try:
        gemini_model = google.beta.realtime.RealtimeModel(
            model=Config.MODEL,
            voice=Config.VOICE,
            temperature=Config.TEMPERATURE,
            instructions=Config.SYSTEM_INSTRUCTIONS,
            api_key=api_key,
        )
        logger.info(f"✅ Initialized Gemini model: {Config.MODEL} with voice: {Config.VOICE}")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Gemini model: {e}")
        return
    
    # Create the voice assistant
    try:
        assistant = VoiceAssistant(
            vad=rtc.VAD.create_silero_vad(),  # Voice activity detection
            stt=google.stt.STT(),  # Speech-to-text
            llm=gemini_model,  # Use Gemini Live API directly
            tts=google.tts.TTS(),  # Text-to-speech (fallback)
            chat_ctx=llm.ChatContext(),  # Chat context
        )
        logger.info("✅ Created voice assistant with Gemini Live API")
        
    except Exception as e:
        logger.error(f"❌ Failed to create voice assistant: {e}")
        return
    
    # Start the assistant
    try:
        assistant.start(ctx.room)
        logger.info("✅ Voice assistant started successfully")
        
        # Wait for participants and handle the session
        async def on_participant_connected(participant: rtc.RemoteParticipant):
            logger.info(f"👤 Participant connected: {participant.identity}")
            
            # The VoiceAssistant will automatically handle audio from participants
            # and respond using the Gemini Live API
        
        ctx.room.on("participant_connected", on_participant_connected)
        
        # Keep the agent running
        logger.info("🎉 Voice Receptionist Agent is ready and waiting for participants!")
        
    except Exception as e:
        logger.error(f"❌ Failed to start voice assistant: {e}")
        return


if __name__ == "__main__":
    # Start the CLI with our entrypoint
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
