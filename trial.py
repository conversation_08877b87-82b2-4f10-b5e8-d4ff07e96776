"""
Trial implementation: Custom Gemini Native Audio Node with LiveKit
Using direct Google GenAI integration bypassing LiveKit's plugin system
"""
import asyncio
import logging
import json
import base64
from typing import Optional, AsyncIterator
import io
import numpy as np

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    WorkerOptions,
    cli,
)
from livekit import rtc
import google.genai as genai
from google.genai import types

from config import Config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GeminiNativeAudioNode:
    """Custom node for Gemini 2.5 Flash Preview Native Audio Dialog"""

    def __init__(self, api_key: str, voice: str = "Zephyr", temperature: float = 0.7):
        self.api_key = api_key
        self.voice = voice
        self.temperature = temperature
        self.client = None
        self.session = None
        self._connection_manager = None
        self._audio_buffer = []

    async def initialize(self):
        """Initialize the Gemini Live API connection"""
        try:
            # Initialize Google GenAI client with standard API version
            self.client = genai.Client(
                api_key=self.api_key,
                http_options={"api_version": "v1beta"}
            )

            # Configure for native audio dialog with basic settings
            config = types.LiveConnectConfig(
                response_modalities=["AUDIO"],
                speech_config=types.SpeechConfig(
                    voice_config=types.VoiceConfig(
                        prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name=self.voice)
                    )
                ),
            )

            # Store the connection context manager for later use
            self._connection_manager = self.client.aio.live.connect(
                model="gemini-2.5-flash-preview-native-audio-dialog",
                config=config
            )

            logger.info("✅ Gemini Native Audio Node initialized successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini Native Audio Node: {e}")
            return False

    async def start_session(self):
        """Start the live session using the connection manager"""
        try:
            if not self._connection_manager:
                logger.error("Connection manager not initialized")
                return False

            # Start the session using async context manager
            self.session = await self._connection_manager.__aenter__()
            logger.info("✅ Gemini Live session started successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to start Gemini Live session: {e}")
            return False

    async def stop_session(self):
        """Stop the live session"""
        try:
            if self._connection_manager and self.session:
                await self._connection_manager.__aexit__(None, None, None)
                self.session = None
                logger.info("✅ Gemini Live session stopped successfully")

        except Exception as e:
            logger.error(f"❌ Failed to stop Gemini Live session: {e}")

    async def send_audio(self, audio_data: bytes, sample_rate: int = 16000):
        """Send audio data to Gemini Live API"""
        try:
            if not self.session:
                logger.error("Session not initialized")
                return

            # Convert audio to the format expected by Gemini
            # Gemini expects 16-bit PCM, 16kHz, mono
            await self.session.send_realtime_input(
                audio=types.Blob(
                    data=audio_data,
                    mime_type=f"audio/pcm;rate={sample_rate}"
                )
            )

        except Exception as e:
            logger.error(f"Error sending audio: {e}")

    async def send_text_instruction(self, instruction: str):
        """Send text instruction to the model"""
        try:
            if not self.session:
                logger.error("Session not initialized")
                return

            await self.session.send_client_content(
                turns={"role": "user", "parts": [{"text": instruction}]},
                turn_complete=True
            )

        except Exception as e:
            logger.error(f"Error sending instruction: {e}")

    async def receive_audio_stream(self) -> AsyncIterator[bytes]:
        """Receive audio stream from Gemini Live API"""
        try:
            if not self.session:
                logger.error("Session not initialized")
                return

            async for response in self.session.receive():
                if response.data is not None:
                    # Gemini returns audio at 24kHz, 16-bit PCM
                    yield response.data

                if response.text is not None:
                    logger.info(f"Gemini text response: {response.text}")

        except Exception as e:
            logger.error(f"Error receiving audio: {e}")

    async def close(self):
        """Close the Gemini session"""
        try:
            # Stop the live session
            await self.stop_session()
            logger.info("Gemini session closed")
        except Exception as e:
            logger.error(f"Error closing session: {e}")


class CustomVoiceReceptionistAgent(Agent):
    """Custom Voice Receptionist Agent using direct Gemini integration"""

    def __init__(self) -> None:
        # Validate configuration
        Config.validate()

        logger.info(f"Initializing Custom Voice Receptionist")
        logger.info(f"Using model: {Config.GEMINI_MODEL}")
        logger.info(f"Using voice: {Config.GEMINI_VOICE}")

        # Initialize the custom Gemini node
        self.gemini_node = GeminiNativeAudioNode(
            api_key=Config.GOOGLE_API_KEY,
            voice=Config.GEMINI_VOICE,
            temperature=Config.GEMINI_TEMPERATURE
        )

        # Initialize base Agent without LLM (we'll handle it manually)
        super().__init__(
            instructions=Config.SYSTEM_INSTRUCTIONS,
        )

        self._audio_track = None
        self._is_speaking = False
        self._room = None

    async def on_enter(self):
        """Called when the agent enters the room"""
        logger.info("🎤 Agent entered room, initializing Gemini connection...")

        # Initialize Gemini connection
        success = await self.gemini_node.initialize()
        if not success:
            logger.error("Failed to initialize Gemini node")
            return

        # Start the live session
        success = await self.gemini_node.start_session()
        if not success:
            logger.error("Failed to start Gemini session")
            return

        # Send initial greeting instruction
        await self.gemini_node.send_text_instruction(
            "Greet the user warmly and professionally. Say: 'Hello! Welcome to our service. I'm your voice assistant. How can I help you today?'"
        )

        # Start audio processing tasks
        asyncio.create_task(self._process_gemini_audio())

        # Check for existing participants in the room
        logger.info(f"🔍 Checking for existing participants in room...")
        if self._room:
            for participant in self._room.remote_participants.values():
                logger.info(f"👤 Found existing participant: {participant.identity}")
                await self.on_participant_connected(participant)

        logger.info("✅ Custom Voice Receptionist Agent is ready!")

    async def on_participant_connected(self, participant: rtc.RemoteParticipant):
        """Handle new participant connection"""
        logger.info(f"👤 Participant connected: {participant.identity}")
        logger.info(f"📊 Participant has {len(participant.track_publications)} track publications")

        # Subscribe to participant's audio track
        def on_track_subscribed(track: rtc.Track, publication: rtc.TrackPublication, participant: rtc.RemoteParticipant):
            logger.info(f"🎵 Track subscribed: {track.kind}, {track.sid}")
            if track.kind == rtc.TrackKind.KIND_AUDIO:
                logger.info("🎵 Audio track subscribed successfully!")
                self._audio_track = track
                asyncio.create_task(self._process_participant_audio(track))

        def on_track_published(publication: rtc.TrackPublication, participant: rtc.RemoteParticipant):
            logger.info(f"📢 Track published: {publication.sid}, kind: {publication.kind}, subscribed: {publication.subscribed}")
            if publication.kind == rtc.TrackKind.KIND_AUDIO:
                logger.info("🎵 Audio track published, subscribing...")
                publication.set_subscribed(True)

        def on_track_unsubscribed(track: rtc.Track, publication: rtc.TrackPublication, participant: rtc.RemoteParticipant):
            logger.info(f"🔇 Track unsubscribed: {track.kind}, {track.sid}")

        participant.on("track_subscribed", on_track_subscribed)
        participant.on("track_published", on_track_published)
        participant.on("track_unsubscribed", on_track_unsubscribed)

        # Also check for existing tracks
        for publication in participant.track_publications.values():
            logger.info(f"📋 Existing track: {publication.sid}, kind: {publication.kind}, subscribed: {publication.subscribed}")
            if publication.kind == rtc.TrackKind.KIND_AUDIO:
                if publication.track is not None:
                    logger.info("🎵 Found existing audio track, processing...")
                    self._audio_track = publication.track
                    asyncio.create_task(self._process_participant_audio(publication.track))
                else:
                    logger.info("🎵 Audio track exists but not yet available, subscribing...")
                    publication.set_subscribed(True)

    async def _process_participant_audio(self, track: rtc.AudioTrack):
        """Process incoming audio from participant"""
        try:
            logger.info("🎵 Starting to process participant audio")

            # Create audio stream from track
            audio_stream = rtc.AudioStream(track)

            async for frame in audio_stream:
                if not self._is_speaking:
                    logger.debug(f"🎵 Processing audio frame: {frame.sample_rate}Hz, {frame.num_channels} channels, {frame.samples_per_channel} samples")

                    # Convert audio frame to the format Gemini expects
                    # Gemini expects 16-bit PCM, 16kHz, mono

                    # Get raw audio data as numpy array

                    # Convert frame data to numpy array
                    if frame.num_channels == 1:
                        # Mono audio
                        audio_array = np.frombuffer(frame.data, dtype=np.int16)
                    else:
                        # Convert stereo to mono by averaging channels
                        audio_array = np.frombuffer(frame.data, dtype=np.int16)
                        audio_array = audio_array.reshape(-1, frame.num_channels)
                        audio_array = np.mean(audio_array, axis=1).astype(np.int16)

                    # Resample from LiveKit's sample rate (usually 48kHz) to 16kHz for Gemini
                    if frame.sample_rate != 16000:
                        # Simple resampling (for production, use proper resampling library)
                        target_length = int(len(audio_array) * 16000 / frame.sample_rate)
                        indices = np.linspace(0, len(audio_array) - 1, target_length).astype(int)
                        audio_array = audio_array[indices]

                    # Convert back to bytes
                    audio_data = audio_array.tobytes()

                    # Send to Gemini with correct sample rate
                    await self.gemini_node.send_audio(audio_data, 16000)

                    logger.debug(f"📤 Sent {len(audio_data)} bytes to Gemini (resampled to 16kHz)")

        except Exception as e:
            logger.error(f"Error processing participant audio: {e}")

    async def _process_gemini_audio(self):
        """Process audio responses from Gemini and play them"""
        try:
            logger.info("🔊 Starting to process Gemini audio responses")

            # Create audio source for playback
            audio_source = rtc.AudioSource(sample_rate=24000, num_channels=1)
            track = rtc.LocalAudioTrack.create_audio_track("gemini-voice", audio_source)

            # Publish the audio track to the room
            options = rtc.TrackPublishOptions()
            options.source = rtc.TrackSource.SOURCE_MICROPHONE
            publication = await self._room.local_participant.publish_track(track, options)
            logger.info("🎵 Published Gemini audio track to room")

            async for audio_data in self.gemini_node.receive_audio_stream():
                self._is_speaking = True

                logger.info(f"📥 Received {len(audio_data)} bytes of audio from Gemini")

                # Convert audio data to AudioFrame and push to source
                # Gemini returns 24kHz, 16-bit PCM, mono
                try:
                    # Create audio frame from the received data
                    # Assuming the data is already in the correct format (24kHz, 16-bit PCM, mono)
                    samples_per_channel = len(audio_data) // 2  # 16-bit = 2 bytes per sample

                    # Convert bytes to numpy array
                    audio_array = np.frombuffer(audio_data, dtype=np.int16)

                    # Create AudioFrame
                    frame = rtc.AudioFrame.create(
                        sample_rate=24000,
                        num_channels=1,
                        samples_per_channel=samples_per_channel
                    )

                    # Copy audio data to frame - fix the shape issue
                    frame_data = np.frombuffer(frame.data, dtype=np.int16).reshape(-1, 1)
                    frame_data[:, 0] = audio_array[:samples_per_channel]

                    # Push frame to audio source
                    await audio_source.capture_frame(frame)

                except Exception as audio_error:
                    logger.error(f"Error processing audio frame: {audio_error}")

                self._is_speaking = False

        except Exception as e:
            logger.error(f"Error processing Gemini audio: {e}")
            self._is_speaking = False

    async def on_exit(self):
        """Called when agent exits"""
        logger.info("🚪 Agent exiting, closing Gemini connection...")
        await self.gemini_node.close()


async def entrypoint(ctx: JobContext):
    """Main entrypoint for the custom agent"""
    logger.info(f"🚀 Starting Custom Voice Receptionist Agent in room: {ctx.room.name}")

    # Connect to the room
    await ctx.connect()

    # Create the agent and set the room reference
    agent = CustomVoiceReceptionistAgent()
    agent._room = ctx.room

    # Create and start the agent session
    session = AgentSession()
    await session.start(
        agent=agent,
        room=ctx.room,
    )

    logger.info("✅ Custom Voice Receptionist Agent is active!")


if __name__ == "__main__":
    # Start the CLI with our custom worker
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
