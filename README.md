# Gemini 2.5 Flash Preview Native Audio Dialog Integration

This project provides two implementations for integrating Google's Gemini 2.5 Flash Preview Native Audio Dialog model:

1. **LiveKit Integration** (`trial.py`) - Professional voice receptionist agent for LiveKit rooms
2. **Standalone Console App** (`standalone.py`) - Direct voice chat with Gemini for testing and development

## Features

- **Native Audio Processing**: Uses Gemini 2.5 Flash Preview Native Audio Dialog for high-quality voice interactions
- **Professional Voice Receptionist**: Configured with appropriate system instructions for reception tasks
- **LiveKit Integration**: Seamlessly connects to LiveKit Cloud using the Agents framework
- **Audio-Only Focus**: Optimized for voice-only interactions (no video processing)
- **Realtime Model Support**: Properly integrates Gemini's native audio model with LiveKit's Agent framework
- **Easy Configuration**: Simple environment-based configuration

## Prerequisites

- Python 3.8 or higher
- LiveKit Cloud account (or self-hosted LiveKit server)
- Google API key for Gemini API access

## Installation

1. **Clone or download this project**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt

   # For standalone app only, also install:
   pip install pyaudio numpy
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and fill in your credentials:
   - `LIVEKIT_URL`: Your LiveKit server URL (e.g., `wss://your-project.livekit.cloud`)
   - `LIVEKIT_API_KEY`: Your LiveKit API key
   - `LIVEKIT_API_SECRET`: Your LiveKit API secret
   - `GOOGLE_API_KEY`: Your Google Gemini API key

## Getting Your Credentials

### LiveKit Cloud
1. Sign up at [LiveKit Cloud](https://cloud.livekit.io)
2. Create a new project
3. Get your API key and secret from the project settings
4. Your URL will be in the format: `wss://your-project.livekit.cloud`

### Google Gemini API
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Create an API key
3. Copy the API key to your `.env` file

## Usage

### 1. LiveKit Voice Receptionist Agent

Start the voice receptionist agent:

```bash
python trial.py dev
```

The agent will:
1. Connect to your LiveKit room
2. Wait for participants to join
3. Greet new participants and assist with their requests

### 2. Standalone Console App

For direct testing and development, use the standalone console application:

```bash
python standalone.py
```

This provides:
- **Direct voice chat** with Gemini through your microphone and speakers
- **Text input support** for mixed interaction (type messages and press Enter)
- **Real-time audio streaming** without needing LiveKit infrastructure
- **Simple testing environment** for Gemini's native audio capabilities

#### Standalone App Controls:
- **Voice**: Speak into your microphone for voice interaction
- **Text**: Type messages and press Enter for text interaction
- **Quit**: Type 'q' and press Enter to exit gracefully

### Connecting Clients

You can connect to the agent using:
- LiveKit's web SDK
- Mobile apps built with LiveKit SDKs
- SIP/telephony integration
- Any WebRTC-compatible client

### Example Web Client

Create a simple HTML file to test the agent:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Voice Receptionist Test</title>
    <script src="https://unpkg.com/livekit-client/dist/livekit-client.umd.js"></script>
</head>
<body>
    <button id="connect">Connect to Receptionist</button>
    <button id="disconnect" disabled>Disconnect</button>

    <script>
        const connectBtn = document.getElementById('connect');
        const disconnectBtn = document.getElementById('disconnect');
        let room;

        connectBtn.onclick = async () => {
            const token = 'your_client_token'; // Generate this server-side
            room = new LiveKit.Room();

            await room.connect('wss://your-project.livekit.cloud', token);

            // Enable microphone
            await room.localParticipant.enableCameraAndMicrophone(false, true);

            connectBtn.disabled = true;
            disconnectBtn.disabled = false;
        };

        disconnectBtn.onclick = () => {
            room.disconnect();
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
        };
    </script>
</body>
</html>
```

## Configuration

### Voice Options

You can change the voice by modifying `GEMINI_VOICE` in `config.py`. Available voices:
- Puck (default)
- Charon
- Kore
- Fenrir
- Aoede
- Leda
- Orus
- Zephyr

### System Instructions

Modify the `SYSTEM_INSTRUCTIONS` in `config.py` to customize the receptionist's behavior and personality.

### Model Settings

- **Model**: `gemini-2.5-flash-preview-native-audio-dialog` (native audio model)
- **Temperature**: 0.7 (adjustable for more/less creative responses)

## Troubleshooting

### Common Issues

1. **"Missing required environment variables"**
   - Check that all variables in `.env` are set correctly
   - Ensure no placeholder values remain (like "your_api_key")

2. **Connection issues**
   - Verify your LiveKit URL is correct
   - Check that your API credentials are valid
   - Ensure your firewall allows WebSocket connections

3. **Audio issues**
   - Make sure your browser/client has microphone permissions
   - Check that audio devices are working properly

### Logs

The agent provides detailed logging. Check the console output for debugging information.

## Development

### Project Structure

```
├── trial.py             # LiveKit voice receptionist agent
├── standalone.py        # Standalone console application
├── gemini_node.py       # Gemini audio processing node
├── config.py            # Configuration settings
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
└── README.md           # This file
```

### Extending the Agent

You can extend the agent by:
- Adding custom tools/functions
- Implementing additional conversation flows
- Adding integration with external services
- Customizing the voice pipeline

## License

This project is provided as-is for educational and development purposes.
